<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Vendor;
use App\Models\User;
use Illuminate\Support\Str;

class VendorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Nike vendor
        $this->createVendor(
            'Nike', 
            'Leading sportswear and athletic footwear brand known for innovation and performance.',
            'New York',
            'NY',
            'USA',
            'nike-logo.png',
            true
        );

        // Create Adidas vendor
        $this->createVendor(
            'Adidas', 
            'Iconic sportswear brand combining style with performance for athletes and fashion enthusiasts.',
            'Berlin',
            'Berlin',
            'Germany',
            'adidas-logo.png',
            true
        );

        // Create Puma vendor
        $this->createVendor(
            'Puma', 
            'Sports lifestyle brand offering innovative designs for both athletic and casual wear.',
            'Herzogenaurach',
            'Bavaria',
            'Germany',
            'puma-logo.png',
            true
        );
    }

    /**
     * Create a vendor with the given details
     */
    private function createVendor($name, $description, $city, $state, $country, $logo, $featured = false)
    {
        // Create a user for the vendor if it doesn't exist
        $email = Str::slug($name) . '@example.com';
        $user = User::firstOrCreate(
            ['email' => $email],
            [
                'name' => $name . ' Admin',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'role' => 'vendor'
            ]
        );

        // Create or update the vendor
        Vendor::updateOrCreate(
            ['user_id' => $user->id],
            [
                'shop_name' => $name,
                'slug' => Str::slug($name),
                'description' => $description,
                'city' => $city,
                'state' => $state,
                'country' => $country,
                'logo' => $logo,
                'is_approved' => true,
                'is_featured' => $featured
            ]
        );
    }
}
