<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class Product extends Model
{
    use HasFactory, HasSlug, SoftDeletes;

    protected $fillable = [
        'vendor_id',
        'category_id',
        'brand_id',
        'name',
        'slug',
        'description',
        'price',
        'discount_price',
        'image_url',
        'stock_quantity',
        'is_active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean',
        'stock_quantity' => 'integer',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }
    
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }
    
    public function wishlists()
    {
        return $this->hasMany(Wishlist::class);
    }
    
    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }
    
    public function getCurrentPrice()
    {
        return $this->discount_price > 0 ? $this->discount_price : $this->price;
    }
    
    public function getDiscountPercentageAttribute()
    {
        if (!$this->discount_price || $this->discount_price <= 0 || $this->price <= 0) {
            return 0;
        }
        
        return round((($this->price - $this->discount_price) / $this->price) * 100);
    }
    
    public function getRouteKeyName()
    {
        return 'slug';
    }
    
    /**
     * Check if product is on sale
     *
     * @return bool
     */
    public function isOnSale()
    {
        return $this->discount_price > 0 && $this->discount_price < $this->price;
    }

    /**
     * Get the product variants for the product.
     */
    public function variants()
    {
        return $this->hasMany(ProductVariant::class);
    }

    /**
     * Accessor to check if the product has variants.
     */
    public function getHasVariantsAttribute(): bool
    {
        return $this->variants()->exists();
    }

    /**
     * Get the product's image URL.
     *
     * @return string
     */
    public function getImageUrlAttribute(): string
    {
        // Access the raw attribute value safely
        $imageUrlValue = $this->attributes['image_url'] ?? null;

        if ($imageUrlValue && Storage::disk('public')->exists($imageUrlValue)) {
            return Storage::url($imageUrlValue);
        }
        return asset('images/default-product.png'); // Default image
    }

    // Consider how 'stock_quantity' on the Product model itself should behave.
    // It could be the sum of all variant stocks, or the stock of a 'default' non-variant product.
    // For now, we'll assume it's separate or managed manually if variants are not used.

    // If a product has variants, its effective stock might be the sum of its variants' stock.
    public function getEffectiveStockAttribute(): int
    {
        if ($this->has_variants) {
            return $this->variants()->sum('stock_quantity');
        }
        return $this->stock_quantity ?? 0;
    }

    // Similarly, the price displayed might need adjustment if variants exist.
    // This could be the price of the cheapest variant, or the base price.
    // For now, the 'price' attribute on Product is the base price.
    // The ProductVariant model has a 'getPriceAttribute' for its final price.
}
