<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            // Add vendor_id field
            if (!Schema::hasColumn('reviews', 'vendor_id')) {
                $table->foreignId('vendor_id')->nullable()->after('product_id')->constrained('vendors');
            }
            
            // Add vendor_response field
            if (!Schema::hasColumn('reviews', 'vendor_response')) {
                $table->text('vendor_response')->nullable()->after('is_approved');
            }
            
            // Add vendor_response_date field
            if (!Schema::hasColumn('reviews', 'vendor_response_date')) {
                $table->timestamp('vendor_response_date')->nullable()->after('vendor_response');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            $table->dropColumn([
                'vendor_id',
                'vendor_response',
                'vendor_response_date'
            ]);
        });
    }
};
