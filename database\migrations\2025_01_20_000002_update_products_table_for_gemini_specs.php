<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Add requires_platform_delivery field
            if (!Schema::hasColumn('products', 'requires_platform_delivery')) {
                $table->boolean('requires_platform_delivery')
                      ->default(false)
                      ->after('is_active')
                      ->comment('Whether this product requires platform delivery based on vendor zone');
            }
            
            // Ensure stock_quantity field exists (rename from stock if needed)
            if (Schema::hasColumn('products', 'stock') && !Schema::hasColumn('products', 'stock_quantity')) {
                $table->renameColumn('stock', 'stock_quantity');
            } elseif (!Schema::hasColumn('products', 'stock_quantity')) {
                $table->integer('stock_quantity')->default(0)->after('price');
            }
            
            // Add weight and dimensions for ShipBubbles integration
            if (!Schema::hasColumn('products', 'weight')) {
                $table->decimal('weight', 8, 2)->nullable()->after('stock_quantity')->comment('Weight in kg');
            }
            
            if (!Schema::hasColumn('products', 'length')) {
                $table->decimal('length', 8, 2)->nullable()->after('weight')->comment('Length in cm');
            }
            
            if (!Schema::hasColumn('products', 'width')) {
                $table->decimal('width', 8, 2)->nullable()->after('length')->comment('Width in cm');
            }
            
            if (!Schema::hasColumn('products', 'height')) {
                $table->decimal('height', 8, 2)->nullable()->after('width')->comment('Height in cm');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'requires_platform_delivery',
                'weight',
                'length', 
                'width',
                'height'
            ]);
            
            if (Schema::hasColumn('products', 'stock_quantity') && !Schema::hasColumn('products', 'stock')) {
                $table->renameColumn('stock_quantity', 'stock');
            }
        });
    }
};
