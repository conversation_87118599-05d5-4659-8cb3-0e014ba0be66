<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Commission;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class PaymentController extends Controller
{
    /**
     * Initialize Paystack payment
     */
    public function initializePaystack(Request $request)
    {
        // Validate the request
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'postal_code' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'terms' => 'required',
        ]);
        
        // Calculate order totals
        $cartItems = Session::get('cart', []);
        
        if (empty($cartItems)) {
            return redirect()->route('cart.index')
                ->with('error', 'Your cart is empty. Please add products to your cart.');
        }

        // Determine vendor_id from the first cart item
        $firstCartItem = reset($cartItems); // Get the first item
        $vendorId = null;
        if ($firstCartItem && isset($firstCartItem['vendor_id'])) {
            $vendorId = $firstCartItem['vendor_id'];
        } else {
            // Attempt to get vendor_id from product if not directly in cart item, or if cart structure is different
            // This is a fallback, ideally vendor_id should be consistently in the cart item structure
            if ($firstCartItem && isset($firstCartItem['product_id'])) { // Assuming product_id is the key for product's actual ID
                $product = \App\Models\Product::find($firstCartItem['product_id']);
                if ($product) {
                    $vendorId = $product->vendor_id;
                }
            }
        }

        if (is_null($vendorId)) {
            // Log this issue or handle more gracefully
            // For now, redirect back with an error if no vendor_id can be determined
            
            // Check if the key for product ID is just 'id' instead of 'product_id'
            // This part is speculative based on common cart structures
            if ($firstCartItem && isset($firstCartItem['id'])) {
                 $product = \App\Models\Product::find($firstCartItem['id']);
                 if ($product) {
                    $vendorId = $product->vendor_id;
                 }
            }
            if (is_null($vendorId)) { // Re-check after attempting with 'id'
                 return redirect()->route('cart.index')
                    ->with('error', 'Could not determine vendor for the order. Please try again or contact support.');
            }
        }
        
        $subtotal = 0;
        foreach ($cartItems as $item) {
            $subtotal += $item['price'] * $item['quantity'];
        }
        
        $tax = $subtotal * 0.08; // 8% tax
        $shipping = 0; // Free shipping
        $total = $subtotal + $tax + $shipping;
        
        // Create the order in pending status
        $order = Order::create([
            'user_id' => auth()->id(),
            'vendor_id' => $vendorId, // Added vendor_id here
            'order_number' => 'ORD-' . Str::random(10),
            'status' => 'pending',
            'payment_status' => 'pending',
            'payment_method' => 'paystack',
            'subtotal' => $subtotal,
            'tax' => $tax,
            'shipping' => $shipping,
            'total' => $total,
            'shipping_address' => json_encode([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'address' => $request->address,
                'city' => $request->city,
                'state' => $request->state,
                'postal_code' => $request->postal_code,
                'country' => $request->country,
                'notes' => $request->order_notes ?? ''
            ]),
            'notes' => $request->order_notes
        ]);
        
        // Create order items and calculate commissions
        foreach ($cartItems as $id => $item) {
            $orderItem = OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $id,
                'quantity' => $item['quantity'],
                'price' => $item['price'],
                'product_data' => json_encode(['name' => $item['name']])
            ]);
            
            // Create commission record for the vendor
            $product = \App\Models\Product::find($id);
            if ($product && $product->vendor) {
                $commissionRate = 0.10; // 10% commission to the platform
                $commissionAmount = $item['price'] * $item['quantity'] * $commissionRate;
                
                Commission::create([
                    'vendor_id' => $product->vendor_id,
                    'order_id' => $order->id,
                    'order_item_id' => $orderItem->id,
                    'order_amount' => $item['price'] * $item['quantity'],
                    'amount' => $commissionAmount,
                    'status' => 'pending'
                ]);
            }
        }
        
        // Generate Paystack payment data
        $amount = $total * 100; // Convert to kobo (Paystack uses the smallest currency unit)
        $email = $request->email;
        $reference = Str::random(16);
        $callback_url = route('payment.paystack.callback');
        $metadata = json_encode([
            'order_id' => $order->id,
            'order_number' => $order->order_number,
            'custom_fields' => [
                [
                    'display_name' => 'Order Number',
                    'variable_name' => 'order_number',
                    'value' => $order->order_number
                ]
            ]
        ]);
        
        // Store reference in the session
        Session::put('paystack_reference', $reference);
        Session::put('order_id', $order->id);
        
        // Prepare Paystack URL
        $paystack_url = "https://api.paystack.co/transaction/initialize";
        
        // Set Paystack API Key - This should be in your .env file
        $secret_key = config('services.paystack.secret_key');
        
        // Prepare the fields
        $fields = [
            'email' => $email,
            'amount' => $amount,
            'reference' => $reference,
            'callback_url' => $callback_url,
            'metadata' => $metadata
        ];
        
        // Make request to Paystack API
        $fields_string = http_build_query($fields);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $paystack_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields_string);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer " . $secret_key,
            "Cache-Control: no-cache"
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        curl_close($ch);
        
        $result = json_decode($response, true);
        
        // If initialization was successful, redirect to Paystack payment page
        if ($result && $result['status']) {
            // Empty the cart after creating the order
            Session::forget('cart');
            
            return redirect($result['data']['authorization_url']);
        } else {
            // If there was an error, show error message
            return redirect()->route('checkout.index')
                ->with('error', 'Unable to initialize payment. Please try again later.');
        }
    }
    
    /**
     * Handle Paystack payment callback
     */
    public function handlePaystackCallback(Request $request)
    {
        $reference = $request->reference;
        if (!$reference) {
            return redirect()->route('checkout.index')
                ->with('error', 'Payment was not successful. Please try again.');
        }
        
        // Verify transaction with Paystack
        $secret_key = config('services.paystack.secret_key');
        $curl = curl_init();
        
        curl_setopt_array($curl, [
            CURLOPT_URL => "https://api.paystack.co/transaction/verify/" . rawurlencode($reference),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                "Authorization: Bearer " . $secret_key,
                "Cache-Control: no-cache"
            ],
        ]);
        
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        
        if ($err) {
            // There was an error during the verification
            return redirect()->route('checkout.index')
                ->with('error', 'Payment verification failed. Please contact support.');
        }
        
        $transaction = json_decode($response);
        
        // Check if transaction was successful
        if ($transaction->status && $transaction->data->status === 'success') {
            // Get the order ID from session
            $order_id = Session::get('order_id');
            $order = Order::find($order_id);
            
            if ($order) {
                // Update order status
                $order->payment_status = 'paid';
                $order->status = 'processing';
                $order->payment_details = json_encode([
                    'gateway' => 'paystack',
                    'reference' => $reference,
                    'amount' => $transaction->data->amount / 100, // Convert back from kobo
                    'paid_at' => now()->toDateTimeString()
                ]);
                $order->save();
                
                // Clear session data
                Session::forget('paystack_reference');
                Session::forget('order_id');
                
                // Redirect to success page
                return redirect()->route('checkout.success', ['order' => $order->id])
                    ->with('success', 'Payment was successful! Your order is now being processed.');
            }
        }
        
        // If payment failed or order not found
        return redirect()->route('checkout.index')
            ->with('error', 'Payment was not successful. Please try again.');
    }
    
    /**
     * Display checkout success page
     */
    public function checkoutSuccess(Order $order)
    {
        // Ensure the order belongs to the authenticated user
        if ($order->user_id !== auth()->id()) {
            abort(403, 'Unauthorized action.');
        }
        
        return view('checkout.success', compact('order'));
    }
}
