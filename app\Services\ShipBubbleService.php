<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Vendor;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class ShipBubbleService
{
    protected $apiKey;
    protected $apiUrl;
    protected $timeout;

    public function __construct()
    {
        $this->apiKey = config('services.shipbubble.api_key') ?? env('SHIPBUBBLE_API_KEY');
        $this->apiUrl = config('ecommerce.shipping.shipbubbles.api_url');
        $this->timeout = config('ecommerce.shipping.shipbubbles.timeout', 30);
    }

    /**
     * Create a shipment for an order
     *
     * @param Order $order
     * @return array|null
     */
    public function createShipment(Order $order): ?array
    {
        try {
            if (!$this->isEnabled()) {
                Log::info('ShipBubbles integration is disabled');
                return null;
            }

            if (!$this->apiKey) {
                throw new Exception('ShipBubbles API key not configured');
            }

            // Get platform delivery items only
            $platformDeliveryItems = $order->items()
                ->whereHas('product', function ($query) {
                    $query->where('requires_platform_delivery', true);
                })
                ->with(['product', 'vendor'])
                ->get();

            if ($platformDeliveryItems->isEmpty()) {
                Log::info("No platform delivery items found for order {$order->order_number}");
                return null;
            }

            // Group items by vendor for multiple shipments if needed
            $itemsByVendor = $platformDeliveryItems->groupBy('vendor_id');
            $shipments = [];

            foreach ($itemsByVendor as $vendorId => $items) {
                $vendor = $items->first()->vendor;
                $shipmentData = $this->prepareShipmentData($order, $vendor, $items);
                
                $response = $this->makeApiCall('/shipments', 'POST', $shipmentData);
                
                if ($response && isset($response['data'])) {
                    $shipments[] = $response['data'];
                    
                    // Update order with shipment ID (for single vendor orders)
                    if ($itemsByVendor->count() === 1) {
                        $order->update([
                            'shipbubble_shipment_id' => $response['data']['id'] ?? null,
                            'status' => 'awaiting_platform_pickup'
                        ]);
                    }
                    
                    Log::info("Shipment created successfully for vendor {$vendorId}, order {$order->order_number}");
                } else {
                    Log::error("Failed to create shipment for vendor {$vendorId}, order {$order->order_number}");
                }
            }

            return $shipments;

        } catch (Exception $e) {
            Log::error('ShipBubbles shipment creation failed: ' . $e->getMessage(), [
                'order_id' => $order->id,
                'order_number' => $order->order_number
            ]);
            return null;
        }
    }

    /**
     * Prepare shipment data for API call
     *
     * @param Order $order
     * @param Vendor $vendor
     * @param \Illuminate\Support\Collection $items
     * @return array
     */
    protected function prepareShipmentData(Order $order, Vendor $vendor, $items): array
    {
        $totalValue = $items->sum(function ($item) {
            return $item->price_at_purchase * $item->quantity;
        });

        $totalWeight = $items->sum(function ($item) {
            return ($item->product->weight ?? config('ecommerce.shipping.default_weight')) * $item->quantity;
        });

        return [
            'pickup_address' => [
                'name' => $vendor->business_name ?? $vendor->shop_name,
                'phone' => $vendor->contact_phone ?? $vendor->user->phone ?? '',
                'email' => $vendor->contact_email_business ?? $vendor->user->email,
                'address' => $vendor->address_line1 ?? $vendor->address,
                'city' => $vendor->city,
                'state' => $vendor->state,
                'country' => $vendor->country ?? 'Nigeria'
            ],
            'delivery_address' => [
                'name' => $order->shipping_name,
                'phone' => $order->shipping_phone,
                'email' => $order->user->email,
                'address' => $order->shipping_address,
                'city' => $order->shipping_city,
                'state' => $order->shipping_state,
                'postal_code' => $order->shipping_postal_code,
                'country' => $order->shipping_country ?? 'Nigeria'
            ],
            'package_details' => [
                'weight' => $totalWeight,
                'length' => config('ecommerce.shipping.default_dimensions.length'),
                'width' => config('ecommerce.shipping.default_dimensions.width'),
                'height' => config('ecommerce.shipping.default_dimensions.height'),
                'description' => 'E-commerce package',
                'value' => $totalValue
            ],
            'items' => $items->map(function ($item) {
                return [
                    'name' => $item->product_name,
                    'quantity' => $item->quantity,
                    'value' => $item->price_at_purchase,
                    'weight' => $item->product->weight ?? config('ecommerce.shipping.default_weight')
                ];
            })->toArray(),
            'reference' => $order->order_number,
            'delivery_type' => 'standard',
            'payment_type' => 'prepaid'
        ];
    }

    /**
     * Track a shipment
     *
     * @param string $shipmentId
     * @return array|null
     */
    public function trackShipment(string $shipmentId): ?array
    {
        try {
            return $this->makeApiCall("/shipments/{$shipmentId}/track", 'GET');
        } catch (Exception $e) {
            Log::error('ShipBubbles tracking failed: ' . $e->getMessage(), [
                'shipment_id' => $shipmentId
            ]);
            return null;
        }
    }

    /**
     * Make API call to ShipBubbles
     *
     * @param string $endpoint
     * @param string $method
     * @param array $data
     * @return array|null
     */
    protected function makeApiCall(string $endpoint, string $method = 'GET', array $data = []): ?array
    {
        $response = Http::timeout($this->timeout)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ])
            ->$method($this->apiUrl . $endpoint, $data);

        if ($response->successful()) {
            return $response->json();
        }

        Log::error('ShipBubbles API call failed', [
            'endpoint' => $endpoint,
            'method' => $method,
            'status' => $response->status(),
            'response' => $response->body()
        ]);

        return null;
    }

    /**
     * Check if ShipBubbles integration is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return config('ecommerce.shipping.shipbubbles.enabled', false) && !empty($this->apiKey);
    }

    /**
     * Get delivery quote
     *
     * @param array $pickupAddress
     * @param array $deliveryAddress
     * @param array $packageDetails
     * @return array|null
     */
    public function getQuote(array $pickupAddress, array $deliveryAddress, array $packageDetails): ?array
    {
        try {
            $data = [
                'pickup_address' => $pickupAddress,
                'delivery_address' => $deliveryAddress,
                'package_details' => $packageDetails
            ];

            return $this->makeApiCall('/quotes', 'POST', $data);
        } catch (Exception $e) {
            Log::error('ShipBubbles quote request failed: ' . $e->getMessage());
            return null;
        }
    }
}
