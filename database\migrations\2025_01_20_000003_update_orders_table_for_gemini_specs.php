<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Add order_number field
            if (!Schema::hasColumn('orders', 'order_number')) {
                $table->string('order_number')->unique()->after('id');
            }
            
            // Rename total to total_amount for consistency with specs
            if (Schema::hasColumn('orders', 'total') && !Schema::hasColumn('orders', 'total_amount')) {
                $table->renameColumn('total', 'total_amount');
            }
            
            // Add payment gateway reference
            if (!Schema::hasColumn('orders', 'payment_gateway_reference')) {
                $table->string('payment_gateway_reference')->nullable()->after('payment_status');
            }
            
            // Add ShipBubbles shipment ID
            if (!Schema::hasColumn('orders', 'shipbubble_shipment_id')) {
                $table->string('shipbubble_shipment_id')->nullable()->after('payment_gateway_reference');
            }
            
            // Add detailed shipping address fields
            if (!Schema::hasColumn('orders', 'shipping_name')) {
                $table->string('shipping_name')->nullable()->after('shipbubble_shipment_id');
            }
            
            if (!Schema::hasColumn('orders', 'shipping_city')) {
                $table->string('shipping_city')->nullable()->after('shipping_address');
            }
            
            if (!Schema::hasColumn('orders', 'shipping_state')) {
                $table->string('shipping_state')->nullable()->after('shipping_city');
            }
            
            if (!Schema::hasColumn('orders', 'shipping_postal_code')) {
                $table->string('shipping_postal_code')->nullable()->after('shipping_state');
            }
            
            if (!Schema::hasColumn('orders', 'shipping_country')) {
                $table->string('shipping_country')->default('Nigeria')->after('shipping_postal_code');
            }
            
            if (!Schema::hasColumn('orders', 'shipping_phone')) {
                $table->string('shipping_phone')->nullable()->after('shipping_country');
            }
            
            if (!Schema::hasColumn('orders', 'shipping_method')) {
                $table->string('shipping_method')->nullable()->after('shipping_phone');
            }
            
            if (!Schema::hasColumn('orders', 'payment_method')) {
                $table->string('payment_method')->nullable()->after('payment_gateway_reference');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn([
                'order_number',
                'payment_gateway_reference',
                'shipbubble_shipment_id',
                'shipping_name',
                'shipping_city',
                'shipping_state', 
                'shipping_postal_code',
                'shipping_country',
                'shipping_phone',
                'shipping_method',
                'payment_method'
            ]);
            
            if (Schema::hasColumn('orders', 'total_amount') && !Schema::hasColumn('orders', 'total')) {
                $table->renameColumn('total_amount', 'total');
            }
        });
    }
};
