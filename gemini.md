**Project Objective:** Enhance an existing Laravel multivendor e-commerce application targeting the Nigerian market. This involves implementing new features for vendor management, product management, order processing (including Paystack and ShipBubbles integration), customer management, search, reviews, and an admin dashboard. Adhere to Nigerian market specifics (delivery zones, pricing, payment methods) and Laravel best practices.

**General Instructions for Gemini 2.5 Flash:**
For each module below, assume you are modifying or adding to an existing Laravel application.

1.  **Analyze the Requirement:** Understand the goal of the module.
2.  **Implement in Laravel:** Provide code structures, and where appropriate, example code snippets for:
    -   **Models:** Define attributes, relationships, and necessary fillable/guarded properties. Include migrations.
    -   **Controllers:** Outline methods for CRUD operations and business logic.
    -   **Routes:** Define web routes (e.g., in `routes/web.php`), appropriately grouped and middleware-protected.
    -   **Validation:** Specify validation rules for input data, preferably using Laravel Form Requests.
    -   **Services:** For external API integrations (Paystack, ShipBubbles) or complex business logic, suggest creating service classes (e.g., `App\Services\ShipBubbleService`).
    -   **Middleware/Policies:** For authentication and authorization (e.g., 'isVendor', 'isAdmin', ownership policies).
    -   **Blade Views (Conceptual):** Describe what forms/information views should display.
    -   **Events/Listeners/Queues:** Suggest where these might be useful (e.g., sending emails, processing ShipBubbles after payment).
3.  **External Integrations:**
    -   For Paystack: Detail setup (env keys), payment initiation, and webhook handling.
    -   For ShipBubbles: Detail setup (env keys: `SHIPBUBBLE_API_KEY`), how to structure API calls within a service class (`ShipBubbleService`), what data is needed (pickup, delivery, items, dimensions), and when to trigger shipment creation (after successful payment for platform-delivered items). Assume access to ShipBubbles API documentation for specific endpoint details.
4.  **Database Schema:** Ensure the described models translate into a coherent database schema.

---

**Modules to Implement:**

**Module 1: Enhanced Vendor Management**

-   **Goal:** Robust vendor registration, login, profiles, and tiered subscriptions based on delivery zones.
-   **Tasks:**
    -   **User Role:** Ensure `User` model has a `role` (e.g., 'vendor').
    -   **Vendor Model (`Vendor`):**
        -   Attributes: `user_id` (FK), `business_name`, `address_line1`, `city`, `state`, `contact_phone`, `contact_email_business`, `delivery_zone_type` (ENUM: 'platform_delivery_zone', 'other_states_zone'), `subscription_status` (ENUM: 'active', 'inactive', 'pending_payment', 'expired'), `approved` (BOOLEAN, default false).
        -   Relationships: `User` hasOne `Vendor`, `Vendor` belongsTo `User`.
    -   **Registration:**
        -   Collect vendor details. Determine `delivery_zone_type` based on `state` (Lagos, Abuja, Ibadan, Akure = 'platform_delivery_zone', others = 'other_states_zone').
        -   Display delivery policy: Platform delivery only for 'platform_delivery_zone' states, on Mon, Wed, Fri.
        -   Display subscription fee: 10,000 NGN for 'platform_delivery_zone', 7,000 NGN for 'other_states_zone'.
        -   Initial status: `approved = false`, `subscription_status = 'pending_payment'`.
        -   Validation: Business name, address, state, Nigerian phone.
    -   **Profile Management:** CRUD for vendor-editable profile details.
    -   **Subscription Model (`Subscription`):**
        -   Attributes: `vendor_id`, `plan_name`, `amount_paid`, `start_date`, `end_date`, `status`, `paystack_reference`.
        -   Integrate Paystack for subscription payments. On success, update `vendors.subscription_status` to 'active' and create `Subscription` record.
        -   Scheduled task to check for expired subscriptions.
    -   **Authorization:** Use middleware (`IsVendor`) and Policies.

**Module 2: Product Management with Commission**

-   **Goal:** Allow active vendors to manage products; implement platform commission.
-   **Tasks:**
    -   **Product Model (`Product`):**
        -   Attributes: `vendor_id` (FK), `name`, `slug`, `description`, `price` (DECIMAL), `stock_quantity` (INTEGER), `is_active` (BOOLEAN), `requires_platform_delivery` (BOOLEAN, based on vendor's zone).
        -   Consider `spatie/laravel-medialibrary` for images.
        -   Relationships: `Vendor` hasMany `Product`, `Product` belongsTo `Vendor`.
        -   (Optional) `ProductVariation` model if variations are needed.
    -   **Product CRUD:** Vendor-only, policy-protected. Set `requires_platform_delivery` on creation.
    -   **Validation:** Name, description, price > 0, stock >= 0.
    -   **Commission:** Platform earns 2.7% on sales. Store rate in `config/ecommerce.php` or `.env`. Calculate and record per order item.

**Module 3: Order Management (with Paystack & ShipBubbles)**

-   **Goal:** Customer order placement, history, tracking. Integrate Paystack and ShipBubbles.
-   **Tasks:**
    -   **Order Models:**
        -   `Order`: `customer_id` (FK to `users`), `order_number`, `total_amount`, `status` (ENUMs), `shipping_address_details`, `payment_gateway_reference`, `shipbubble_shipment_id` (nullable).
        -   `OrderItem`: `order_id`, `product_id`, `vendor_id`, `quantity`, `price_at_purchase`, `commission_rate_snapshot`, `commission_amount_calculated`.
    -   **Cart:** Session/DB-based.
    -   **Checkout:**
        -   Collect shipping info.
        -   Display delivery policy (platform delivery for specific states; vendor handles others).
        -   Paystack integration (e.g., `unicodeveloper/laravel-paystack`): Initiate payment.
    -   **Paystack Webhook:**
        -   Route for `charge.success`. Verify event.
        -   Update `Order` status to 'processing'. Record Paystack reference.
        -   Calculate `commission_amount_calculated` for `OrderItems`.
        -   Decrement product stock.
        -   Send notifications (customer, vendor).
        -   **Trigger ShipBubbles integration.**
    -   **ShipBubbles Integration (`ShipBubbleService`):**
        -   Trigger after successful payment for orders/items where `products.requires_platform_delivery` is true.
        -   `createShipment(Order $order)` method in `ShipBubbleService`.
        -   API Call Data: Vendor address (pickup), customer address (delivery), recipient details, item list (name, qty, value), package dimensions/weight (consider adding these to `Product` model or using defaults).
        -   Store `shipbubble_shipment_id` and tracking number on the `Order` or a dedicated `Shipments` table if multiple shipments per order for different platform-delivery vendors.
        -   Update order status (e.g., 'shipped' or 'awaiting_platform_pickup').
        -   Handle API errors gracefully.
    -   **Order History:** Customer view with status and tracking.

**Module 4: Customer Management**

-   **Goal:** Standard customer registration, login, profile.
-   **Tasks:** (Likely leverage existing Laravel auth)
    -   `User` model with `role` ('customer').
    -   Registration (name, email, password). Bcrypt for passwords.
    -   Login.
    -   Profile management (details, addresses).

**Module 5: Search and Filtering**

-   **Goal:** Search products by name, category, vendor; filter by price, rating, availability.
-   **Tasks:**
    -   Eloquent-based search: `WHERE LIKE`, `whereHas` for vendor search.
    -   Filtering: Query scopes on `Product` model (e.g., `scopePriceBetween`).
    -   (Future) Consider Laravel Scout.

**Module 6: Rating and Review**

-   **Goal:** Customers rate/review products; vendors respond.
-   **Tasks:**
    -   **Review Model (`Review`):**
        -   Attributes: `customer_id`, `product_id`, `vendor_id`, `rating` (1-5), `comment`, `is_approved` (BOOLEAN), `vendor_response` (TEXT).
        -   Relationships: BelongsTo `User`, `Product`, `Vendor`. `Product` hasMany `Review`.
    -   Customers (who purchased) submit reviews.
    -   Vendors respond to reviews on their products.
    -   Display average rating and reviews on product pages.

**Module 7: Admin Dashboard**

-   **Goal:** Centralized management for administrators.
-   **Tasks:**
    -   Admin routes (e.g., `/admin`) with `IsAdmin` middleware.
    -   **Management Sections (CRUD interfaces):**
        -   Vendors: List, view, approve, manage subscriptions.
        -   Products: View all, edit, delete, flag.
        -   Orders: View all, filter, update status.
        -   Customers: View, manage accounts.
        -   Reviews: Moderate.
    -   **Analytics/Reporting:**
        -   Dashboard for subscription revenue, platform commission.
        -   Reports: Commission per vendor, sales per vendor.

**Module 8: Security, Best Practices & Compliance (Apply throughout)**

-   **Authentication/Authorization:** Laravel Sanctum/web sessions. Gates & Policies.
-   **Input Validation:** Laravel Validator (Form Requests).
-   **Error Handling/Logging:** Laravel's logging, custom exceptions.
-   **Rate Limiting:** Apply to sensitive routes.
-   **Environment Variables (`.env`):** For ALL secrets (DB, Paystack, ShipBubble keys).
-   **SSL/TLS:** Enforce HTTPS in production.
-   **NDPR Compliance:** Consent, data minimization, privacy policy.
-   **Scalability:** Eager loading, query optimization, Laravel Queues (for emails, ShipBubbles calls).

---

Start by outlining the model structure and migrations for Module 1. Then proceed to its controllers and routes. We can build this iteratively, module by module.
