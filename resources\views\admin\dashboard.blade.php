@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-3 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Sales</h6>
                            <h3 class="fw-bold mb-0">${{ number_format($totalSales, 2) }}</h3>
                        </div>
                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                            <i class="fas fa-dollar-sign text-success"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="badge bg-success">{{ App\Models\Order::where('created_at', '>=', now()->subDays(30))->count() }} orders</span>
                        <span class="text-muted ms-2">in last 30 days</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Orders</h6>
                            <h3 class="fw-bold mb-0">{{ $totalOrders }}</h3>
                        </div>
                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                            <i class="fas fa-shopping-cart text-primary"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="badge bg-info">{{ App\Models\Order::where('status', 'pending')->count() }}</span>
                        <span class="text-muted ms-2">pending orders</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Customers</h6>
                            <h3 class="fw-bold mb-0">{{ $totalCustomers }}</h3>
                        </div>
                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                            <i class="fas fa-users text-info"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        @php
                            $newCustomers = App\Models\User::whereHas('role', function($q) { 
                                $q->where('name', 'customer'); 
                            })
                            ->where('created_at', '>=', now()->subDays(30))
                            ->count();
                        @endphp
                        <span class="badge bg-success">+{{ $newCustomers }}</span>
                        <span class="text-muted ms-2">new this month</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Vendors</h6>
                            <h3 class="fw-bold mb-0">{{ $totalVendors }}</h3>
                        </div>
                        <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                            <i class="fas fa-store text-warning"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="badge bg-danger">{{ App\Models\Vendor::where('is_approved', false)->count() }}</span>
                        <span class="text-muted ms-2">pending approval</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Orders</h5>
                    <a href="{{ route('admin.orders.index') }}" class="btn btn-sm btn-dark">View All</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped mb-0">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Customer</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentOrders as $order)
                                <tr>
                                    <td>{{ $order->order_number ?? 'ORD-' . $order->id }}</td>
                                    <td>{{ $order->user->name ?? 'Guest' }}</td>
                                    <td>
                                        @if($order->status == 'completed')
                                            <span class="badge bg-success">Completed</span>
                                        @elseif($order->status == 'processing')
                                            <span class="badge bg-warning">Processing</span>
                                        @elseif($order->status == 'cancelled')
                                            <span class="badge bg-danger">Cancelled</span>
                                        @else
                                            <span class="badge bg-info">Pending</span>
                                        @endif
                                    </td>
                                    <td>${{ number_format($order->total, 2) }}</td>
                                    <td><a href="{{ route('admin.orders.show', $order->id) }}" class="btn btn-sm btn-dark">View</a></td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center">No recent orders found</td>
                                </tr>
                                @endforelse
                                    <td>Michael Wilson</td>
                                    <td>May 8, 2024</td>
                                    <td>$145.00</td>
                                    <td><span class="badge bg-danger">Cancelled</span></td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-outline-dark">View</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Pending Vendors</h5>
                    <a href="{{ route('admin.vendors.index') }}" class="btn btn-sm btn-dark">View All</a>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        @forelse($pendingVendors as $vendor)
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $vendor->shop_name }}</h6>
                                    <p class="text-muted mb-0">{{ $vendor->user->name }}</p>
                                </div>
                                <div>
                                    <form action="{{ route('admin.vendors.approve', $vendor->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-sm btn-success me-1">Approve</button>
                                    </form>
                                    <form action="{{ route('admin.vendors.reject', $vendor->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-sm btn-danger">Reject</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        @empty
                        <div class="list-group-item">
                            <p class="text-center mb-0">No pending vendors</p>
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-dark">
                            <i class="fas fa-box me-2"></i> Manage Products
                        </a>
                        <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-dark">
                            <i class="fas fa-tags me-2"></i> Manage Categories
                        </a>
                        <a href="{{ route('admin.users.index') }}" class="btn btn-outline-dark">
                            <i class="fas fa-users me-2"></i> Manage Users
                        </a>
                        <a href="{{ route('admin.settings.index') }}" class="btn btn-outline-dark">
                            <i class="fas fa-cog me-2"></i> Site Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
