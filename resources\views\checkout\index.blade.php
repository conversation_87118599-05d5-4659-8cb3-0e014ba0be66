@extends('layouts.app')

@section('content')
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-decoration-none text-dark">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('cart.index') }}" class="text-decoration-none text-dark">Shopping Cart</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Checkout</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <h1 class="fw-bold mb-4">Checkout</h1>
        </div>
    </div>
    
    <form action="{{ route('payment.paystack.initialize') }}" method="POST">
        @csrf
        <div class="row">
            <!-- Checkout Form -->
            <div class="col-lg-8 mb-4">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white py-3">
                        <h5 class="fw-bold mb-0">Shipping Information</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="phone" name="phone" required>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="address" name="address" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="city" class="form-label">City <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="city" name="city" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="state" class="form-label">State/Province <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="state" name="state" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="postal_code" class="form-label">Postal Code <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code" required>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="country" class="form-label">Country <span class="text-danger">*</span></label>
                                <select class="form-select" id="country" name="country" required>
                                    <option value="">Select Country</option>
                                    <option value="US">United States</option>
                                    <option value="CA">Canada</option>
                                    <option value="UK">United Kingdom</option>
                                    <option value="AU">Australia</option>
                                    <option value="NG">Nigeria</option>
                                    <!-- Add more countries as needed -->
                                </select>
                            </div>
                            <div class="col-12">
                                <label for="order_notes" class="form-label">Order Notes (Optional)</label>
                                <textarea class="form-control" id="order_notes" name="order_notes" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="fw-bold mb-0">Payment Method</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="radio" name="payment_method" id="paystack" value="paystack" checked>
                            <label class="form-check-label" for="paystack">
                                Paystack
                            </label>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="radio" name="payment_method" id="bank_transfer" value="bank_transfer">
                            <label class="form-check-label" for="bank_transfer">
                                Bank Transfer
                            </label>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="radio" name="payment_method" id="cash_on_delivery" value="cash_on_delivery">
                            <label class="form-check-label" for="cash_on_delivery">
                                Cash on Delivery
                            </label>
                        </div>
                        
                        <div id="paystack_details" class="mt-4">
                            <div class="alert alert-info">
                                <p class="mb-0">You will be redirected to Paystack to complete your payment.</p>
                            </div>
                        </div>
                        
                        <div id="bank_transfer_details" class="mt-4 d-none">
                            <div class="alert alert-info">
                                <p class="fw-bold mb-2">Bank Account Details:</p>
                                <p class="mb-1">Bank Name: First Bank</p>
                                <p class="mb-1">Account Name: Brandify Ltd</p>
                                <p class="mb-1">Account Number: **********</p>
                                <p class="mb-0">Please use your Order ID as the payment reference.</p>
                            </div>
                        </div>
                        
                        <div id="cash_on_delivery_details" class="mt-4 d-none">
                            <div class="alert alert-info">
                                <p class="mb-0">You will pay when the order is delivered to your address.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Order Summary -->
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="fw-bold mb-0">Order Summary</h5>
                    </div>
                    <div class="card-body">
                        @if(session('cart') && count(session('cart')) > 0)
                            @php
                                $subtotal = 0;
                            @endphp
                            @foreach(session('cart') as $id => $item)
                                @php
                                    $itemTotal = $item['price'] * $item['quantity'];
                                    $subtotal += $itemTotal;
                                @endphp
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>{{ $item['name'] }} x {{ $item['quantity'] }}</span>
                                    <span class="fw-bold">${{ number_format($itemTotal, 2) }}</span>
                                </div>
                            @endforeach
                            <hr>
                            @php
                                $shipping = 0;
                                $taxRate = 0.08; // 8% tax rate
                                $tax = $subtotal * $taxRate;
                                $total = $subtotal + $tax + $shipping;
                            @endphp
                            <div class="d-flex justify-content-between mb-3">
                                <span>Subtotal</span>
                                <span class="fw-bold">${{ number_format($subtotal, 2) }}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>Shipping</span>
                                <span class="fw-bold">${{ number_format($shipping, 2) }}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>Tax (8%)</span>
                                <span class="fw-bold">${{ number_format($tax, 2) }}</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-4">
                                <span class="fw-bold">Total</span>
                                <span class="fw-bold fs-5">${{ number_format($total, 2) }}</span>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <p class="text-muted mb-2">Your cart is empty</p>
                                <a href="{{ route('products.index') }}" class="btn btn-dark">Continue Shopping</a>
                            </div>
                        @endif
                        
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" class="text-decoration-none">Terms and Conditions</a>
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-dark w-100 py-3 fw-bold">
                            Place Order
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const paystackRadio = document.getElementById('paystack');
        const bankTransferRadio = document.getElementById('bank_transfer');
        const cashOnDeliveryRadio = document.getElementById('cash_on_delivery');
        
        const paystackDetails = document.getElementById('paystack_details');
        const bankTransferDetails = document.getElementById('bank_transfer_details');
        const cashOnDeliveryDetails = document.getElementById('cash_on_delivery_details');
        
        function togglePaymentDetails() {
            paystackDetails.classList.add('d-none');
            bankTransferDetails.classList.add('d-none');
            cashOnDeliveryDetails.classList.add('d-none');
            
            if (paystackRadio.checked) {
                paystackDetails.classList.remove('d-none');
            } else if (bankTransferRadio.checked) {
                bankTransferDetails.classList.remove('d-none');
            } else if (cashOnDeliveryRadio.checked) {
                cashOnDeliveryDetails.classList.remove('d-none');
            }
        }
        
        paystackRadio.addEventListener('change', togglePaymentDetails);
        bankTransferRadio.addEventListener('change', togglePaymentDetails);
        cashOnDeliveryRadio.addEventListener('change', togglePaymentDetails);
        
        // Initialize
        togglePaymentDetails();
    });
</script>
@endpush
@endsection
