@extends('layouts.app')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <h1 class="fw-bold mb-2">Become a Vendor</h1>
                <p class="text-muted">Join our marketplace and start selling your products to thousands of customers.</p>
            </div>
            
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <ul class="nav nav-pills card-header-pills">
                        <li class="nav-item">
                            <a class="nav-link active" href="#registration" data-bs-toggle="tab">
                                <i class="fas fa-user-plus me-2"></i> Registration
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link disabled" href="#verification" data-bs-toggle="tab">
                                <i class="fas fa-check-circle me-2"></i> Verification
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link disabled" href="#setup" data-bs-toggle="tab">
                                <i class="fas fa-store me-2"></i> Store Setup
                            </a>
                        </li>
                    </ul>
                </div>
                
                <div class="card-body p-4">
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="registration">
                            <form method="POST" action="{{ route('vendor.store') }}" enctype="multipart/form-data">
                                @csrf
                                
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h5 class="fw-bold mb-3">Personal Information</h5>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email') }}" required>
                                        @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                        <input type="password" class="form-control @error('password') is-invalid @enderror" id="password" name="password" required>
                                        @error('password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="text-muted">Password must be at least 8 characters long</small>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="password_confirmation" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                        <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone') }}" required>
                                        @error('phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h5 class="fw-bold mb-3">Business Information</h5>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="business_name" class="form-label">Business Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('business_name') is-invalid @enderror" id="business_name" name="business_name" value="{{ old('business_name') }}" required>
                                        @error('business_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="logo" class="form-label">Business Logo</label>
                                        <input type="file" class="form-control @error('logo') is-invalid @enderror" id="logo" name="logo">
                                        @error('logo')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="text-muted">Recommended size: 400x400px, max 2MB</small>
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label for="business_address" class="form-label">Business Address <span class="text-danger">*</span></label>
                                        <textarea class="form-control @error('business_address') is-invalid @enderror" id="business_address" name="business_address" rows="2" required>{{ old('business_address') }}</textarea>
                                        @error('business_address')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label for="business_description" class="form-label">Business Description <span class="text-danger">*</span></label>
                                        <textarea class="form-control @error('business_description') is-invalid @enderror" id="business_description" name="business_description" rows="4" required>{{ old('business_description') }}</textarea>
                                        @error('business_description')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="text-muted">Tell us about your business, products, and what makes you unique (minimum 100 characters)</small>
                                    </div>
                                </div>
                                
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h5 class="fw-bold mb-3">Verification Documents</h5>
                                        <p class="text-muted small mb-3">These documents are required to verify your identity and business. They will be securely stored and only accessible to our verification team.</p>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="id_document" class="form-label">ID Document <span class="text-danger">*</span></label>
                                        <input type="file" class="form-control @error('id_document') is-invalid @enderror" id="id_document" name="id_document" required>
                                        @error('id_document')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="text-muted">Upload a copy of your ID card, passport, or driver's license (JPG, PNG, PDF, max 5MB)</small>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="business_document" class="form-label">Business Document</label>
                                        <input type="file" class="form-control @error('business_document') is-invalid @enderror" id="business_document" name="business_document">
                                        @error('business_document')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="text-muted">Upload a business registration certificate, license, or other relevant document (JPG, PNG, PDF, max 5MB)</small>
                                    </div>
                                </div>
                                
                                <div class="form-check mb-4">
                                    <input class="form-check-input @error('terms') is-invalid @enderror" type="checkbox" id="terms" name="terms" required>
                                    <label class="form-check-label" for="terms">
                                        I agree to the <a href="{{ route('terms') }}" target="_blank">Terms of Service</a> and <a href="{{ route('privacy') }}" target="_blank">Privacy Policy</a>
                                    </label>
                                    @error('terms')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-dark btn-lg">
                                        Submit Application
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-body p-4">
                    <h5 class="fw-bold mb-3">Why Become a Vendor?</h5>
                    
                    <div class="row g-4">
                        <div class="col-md-4">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                        <i class="fas fa-globe text-dark"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="fw-bold mb-1">Global Reach</h6>
                                    <p class="small text-muted mb-0">Access customers from around the world.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                        <i class="fas fa-credit-card text-dark"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="fw-bold mb-1">Secure Payments</h6>
                                    <p class="small text-muted mb-0">Multiple payment options for your convenience.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                        <i class="fas fa-chart-line text-dark"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="fw-bold mb-1">Sales Analytics</h6>
                                    <p class="small text-muted mb-0">Detailed reports to track your business growth.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .nav-pills .nav-link.active {
        background-color: #212529;
    }
</style>
@endpush
