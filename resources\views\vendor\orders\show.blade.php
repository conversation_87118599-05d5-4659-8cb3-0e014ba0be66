@extends('layouts.vendor')

@section('content')
<div class="container-fluid">
    <div class="mb-4">
        <a href="{{ route('vendor.orders.index') }}" class="text-decoration-none text-dark">
            <i class="fas fa-arrow-left me-2"></i> Back to Orders
        </a>
    </div>
    
    <div class="card mb-4">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Order #{{ $order->id }}</h5>
            <span class="badge {{ $order->status == 'completed' ? 'bg-success' : 
                ($order->status == 'processing' ? 'bg-warning text-dark' : 
                ($order->status == 'shipping' ? 'bg-info' : 
                ($order->status == 'cancelled' ? 'bg-danger' : 'bg-secondary'))) }}">
                {{ ucfirst($order->status) }}
            </span>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h6 class="text-uppercase text-muted mb-2">Order Information</h6>
                    <p class="mb-1"><strong>Order Date:</strong> {{ $order->created_at->format('M d, Y H:i') }}</p>
                    <p class="mb-1"><strong>Payment Method:</strong> {{ ucfirst($order->payment_method) }}</p>
                    <p class="mb-1"><strong>Payment Status:</strong> 
                        <span class="badge {{ $order->payment_status == 'paid' ? 'bg-success' : 'bg-warning text-dark' }}">
                            {{ ucfirst($order->payment_status) }}
                        </span>
                    </p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-uppercase text-muted mb-2">Customer Information</h6>
                    <p class="mb-1"><strong>Name:</strong> {{ $order->user->name ?? 'Guest' }}</p>
                    <p class="mb-1"><strong>Email:</strong> {{ $order->user->email ?? $order->email }}</p>
                    <p class="mb-1"><strong>Phone:</strong> {{ $order->phone ?? 'N/A' }}</p>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <h6 class="text-uppercase text-muted mb-2">Shipping Address</h6>
                    <p class="mb-1">{{ $order->shipping_address ?? 'N/A' }}</p>
                    <p class="mb-1">{{ $order->shipping_city ?? '' }}, {{ $order->shipping_state ?? '' }} {{ $order->shipping_zip ?? '' }}</p>
                    <p class="mb-1">{{ $order->shipping_country ?? '' }}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-uppercase text-muted mb-2">Billing Address</h6>
                    <p class="mb-1">{{ $order->billing_address ?? 'Same as shipping address' }}</p>
                    <p class="mb-1">{{ $order->billing_city ?? '' }}, {{ $order->billing_state ?? '' }} {{ $order->billing_zip ?? '' }}</p>
                    <p class="mb-1">{{ $order->billing_country ?? '' }}</p>
                </div>
            </div>
            
            @php
                $vendorItems = $order->items->filter(function($item) {
                    return $item->product && $item->product->vendor_id == auth()->user()->vendor->id;
                });
            @endphp
            
            <h6 class="text-uppercase text-muted mb-3">Order Items</h6>
            <div class="table-responsive mb-4">
                <table class="table table-bordered align-middle">
                    <thead class="bg-light">
                        <tr>
                            <th>Product</th>
                            <th>Price</th>
                            <th>Quantity</th>
                            <th class="text-end">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($vendorItems as $item)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if($item->product && $item->product->image_url)
                                            <img src="{{ $item->product->image_url }}" alt="{{ $item->product_name }}" class="img-thumbnail me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                        @else
                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="fas fa-box text-secondary"></i>
                                            </div>
                                        @endif
                                        <div>
                                            <h6 class="mb-0">{{ $item->product_name }}</h6>
                                            @if($item->product)
                                                <small class="text-muted">SKU: {{ $item->product->id }}</small>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td>${{ number_format($item->price, 2) }}</td>
                                <td>{{ $item->quantity }}</td>
                                <td class="text-end">${{ number_format($item->price * $item->quantity, 2) }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="3" class="text-end"><strong>Subtotal</strong></td>
                            <td class="text-end">
                                @php
                                    $subtotal = 0;
                                    foreach($vendorItems as $item) {
                                        $subtotal += $item->price * $item->quantity;
                                    }
                                @endphp
                                ${{ number_format($subtotal, 2) }}
                            </td>
                        </tr>
                        <tr>
                            <td colspan="3" class="text-end"><strong>Vendor Commission</strong></td>
                            <td class="text-end">
                                @php
                                    $commission = $subtotal * 0.10; // Assuming 10% commission rate
                                @endphp
                                -${{ number_format($commission, 2) }}
                            </td>
                        </tr>
                        <tr>
                            <td colspan="3" class="text-end"><strong>Net Amount</strong></td>
                            <td class="text-end">
                                <strong>${{ number_format($subtotal - $commission, 2) }}</strong>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            
            <div class="mt-4">
                <h6 class="text-uppercase text-muted mb-3">Update Order Status</h6>
                <form action="{{ route('vendor.orders.update-status', $order->id) }}" method="POST" class="d-flex align-items-center">
                    @csrf
                    <select name="status" class="form-select me-2" style="max-width: 200px;">
                        <option value="pending" {{ $order->status == 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="processing" {{ $order->status == 'processing' ? 'selected' : '' }}>Processing</option>
                        <option value="shipping" {{ $order->status == 'shipping' ? 'selected' : '' }}>Shipping</option>
                        <option value="completed" {{ $order->status == 'completed' ? 'selected' : '' }}>Completed</option>
                        <option value="cancelled" {{ $order->status == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                    </select>
                    <button type="submit" class="btn btn-dark">Update Status</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
